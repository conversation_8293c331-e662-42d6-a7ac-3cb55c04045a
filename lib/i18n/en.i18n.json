{"appTitle": "Smart Team Web", "start": "Get Started", "termsOfServiceAndPrivacyPolicy": "<link href=\"termsOfService\">Terms of Service</link> and <link href=\"privacyPolicy\">Privacy Policy</link>", "registerAgreement": "I confirm that I have read and accepted the <link>Membership Agreement</link>, <link>Data Protection and Business Policy</link>, <link>Customer Clarification Text</link>, <link>Privacy and Cookie Policy</link>.", "name": "Name", "description": "Description", "active": "Active", "inactive": "Inactive", "search": "Search", "add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "select": "Select...", "all": "All", "none": "None", "online": "Online", "offline": "Offline", "admin": "Admin", "user": "User", "type": "Type", "title": "Title", "group": "Group", "status": "Status", "open": "Open", "list": "List", "addNew": "Add New", "pick": "Select", "selectColor": "Select Color", "fieldRequired": "Field Required", "noDataFound": "No data found", "previous": "Previous", "next": "Next", "page": "Page", "record": "record", "waiting": "Waiting", "accepted": "Accepted", "declined": "Declined", "done": "Done", "passive": "Passive", "potential": "Potential", "priority": {"low": "Low", "medium": "Medium", "high": "High"}, "taskType": {"visit": "Visit", "collection": "Collection", "service": "Service", "onlineMeeting": "Online Meeting", "phoneCall": "Phone Call"}, "device": {"brands": {"teltonika": "Teltonika", "armoli": "Armoli", "kingwoiot": "<PERSON><PERSON><PERSON>"}, "models": {"modelA": "Model A", "modelB": "Model B", "modelC": "Model C"}, "simOperators": {"vodafone": "Vodafone", "turkcell": "Turkcell", "turkTelekom": "Türk Telekom"}, "addDevice": "Add <PERSON>", "deviceList": "Device List", "activationDate": "Activation Date"}, "vehicle": {"addVehicle": "Add Vehicle", "vehicleList": "Vehicle List", "vehicleBrand": "Vehicle Brand", "vehicleModel": "Vehicle Model", "vehicleType": "Vehicle Type", "brands": {"mercedes": "Mercedes", "bmw": "BMW", "audi": "Audi"}}, "customer": {"customers": "Customers", "addCustomer": "Add Customer", "customerA": "Customer A", "customerB": "Customer B", "customerC": "Customer C", "phoneNumber": "Phone Number", "address": "Address", "openAddress": "Open Address", "findMyLocation": "Find My Location", "fillAllFields": "Please fill all fields"}, "groups": {"groupA": "Group A", "groupB": "Group B", "groupC": "Group C", "groupD": "Group D"}, "dashboard": {"todoList": "Todo List", "instantMovements": "Instant Movements", "instantStoppage": "Instant Stoppage"}, "form": {"openForm": "Open Form", "openFormDescription": "Open form, visible to everyone and cannot be assigned to individuals", "deleteFormConfirm": "Are you sure you want to delete the form titled '{title}'?", "requiredQuestion": "* Indicates a required question", "addOption": "Add option"}, "area": {"areaEntry": "Area Entry", "areaWaiting": "Area Waiting", "areaExit": "Area Exit", "editArea": "Edit Area", "areaName": "Area Name", "areaType": "Area Type"}, "emergency": {"emergencyContactName": "Emergency Contact Name", "emergencyPhone": "Emergency Phone", "emergencyContact": "Emergency Contact"}, "validation": {"linkCouldNotOpen": "Link could not be opened", "phoneNumberExample": "+90 555 555 55 55", "phoneNumberShort": "555 555 55 55", "pleaseSelectValid": "Please select a valid option", "thisFieldRequired": "This field is required", "invalidNumberFormat": "Invalid number format", "pleaseEnterValidNumber": "Please enter a valid number", "pleaseEnterValidBrand": "Please enter a valid brand", "pleaseEnterValidModel": "Please enter a valid model", "pleaseFillRequiredFields": "Please fill required fields", "pleaseSelectRole": "Please select a role", "pleaseSelectAtLeastOneDevice": "Please select at least one device", "pleaseSelectPersonnel": "Please select a personnel", "pleaseAddAtLeastOneQuestion": "Please add at least one question", "fillAllFields": "Please fill all fields", "passwordsDoNotMatch": "Passwords do not match", "endDateCannotBeBeforeStartDate": "End date cannot be before start date"}, "copyright": "Smart Location Technologies Inc. © 2025", "common": {"update": "Update", "create": "Create", "reset": "Reset", "clear": "Clear", "clearAll": "Clear All", "selectAll": "Select All", "expandAll": "Expand All", "collapseAll": "Collapse All", "viewAll": "View All", "filter": "Filter", "clearAllFilters": "Clear All Filters", "copy": "Copy", "preview": "Preview", "detail": "Detail", "details": "Details", "back": "Back", "forward": "Forward", "inspect": "Inspect", "operation": "Operation", "operations": "Operations", "result": "Result", "noResultFound": "No result found", "loadingData": "Loading data...", "pleaseWait": "Please wait...", "tryAgain": "Try again", "areYouSure": "Are you sure?", "deleteConfirmation": "Delete Confirmation", "updateSuccessful": "Update successful", "createSuccessful": "Create successful", "deleteSuccessful": "Delete successful", "operationFailed": "Operation failed", "loginSuccessful": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "send": "Send", "mailSent": "Mail sent", "mailCouldNotBeSent": "Mail could not be sent", "linkExpired": "<PERSON> expired. Please send mail again.", "passwordUpdated": "Password updated. You can login with your new password.", "passwordCouldNotBeUpdated": "Password could not be updated", "findMyLocation": "Find My Location", "selectOption": "Select...", "expenseType": "Expense Type", "expenseDate": "Expense Date", "imei": "IMEI", "kilometer": "Kilometer", "simOperator": "SIM Operator", "simCardNumber": "SIM Card Number", "deviceId": "Device ID", "vehicleBrand": "Vehicle Brand", "vehicleModel": "Vehicle Model", "serverKey": "Server Key", "approvalDate": "Approval Date", "permitType": "Permit Type", "permitCalendar": "Permit Calendar", "permitDuration": "Permit Duration", "permitStart": "Permit Start", "permitEnd": "Permit End", "viewDocument": "View Document", "addNewJob": "Add New Job", "formSubmitter": "Form Submitter", "submissionDate": "Submission Date", "addGpsDevice": "Add GPS Device", "noResultsFound": "No results found", "searchPerson": "Search person...", "assignedUsers": "Assigned users: ", "required": "Required", "selectFieldType": "Select field type", "question": "Question", "addOption": "Add option", "addQuestion": "Add Question", "all": "All", "dailyMeetings": "Daily Meetings", "instantMovements": "Instant Movements", "instantStop": "Instant Stop", "mapScreen": "Map Screen", "editJob": "Edit Job", "todoList": "Todo List", "pleaseSelectAtLeastOneDevice": "Please select at least one device", "deactivateActivate": "Deactivate/Activate", "smartLocationTechnologies": "Smart Location Technologies Inc. © 2025"}, "companyType": {"commercial": "Commercial", "individual": "Individual"}, "company": {"companyOperations": "Company Operations", "companyList": "Company List", "companyPanel": "Company Panel", "companies": "Companies", "companyUpdateSuccessful": "Company updated successfully", "companyCreateSuccessful": "Company created successfully", "companyCouldNotBeUpdated": "Company could not be updated", "companyCouldNotBeCreated": "Company could not be created", "companyInfoNotFound": "Company information not found"}, "addNewCustomer": "Add New Customer", "customerId": "Customer Id", "customerName": "Customer Name", "companyName": "Company Name", "contactName": "Contact Name", "authorizedName": "Authorized Name", "authorizedEmail": "Authorized Email", "contactGSM": "Contact GSM", "authorizedGSM": "Authorized GSM", "contactEmail": "Contact Email", "taxOffice": "Tax Office", "taxNumber": "Tax Number", "customerPhone": "Customer Phone", "webAddress": "Web Address", "userName": "User Name", "password": "Password", "nameSurname": "Name Surname", "email": "Email", "phone": "Phone", "newUser": "New User", "users": "Users", "userDevice": "User Device", "userType": "User Type", "roleType": "Role Type", "passAgain": "Password(Again)", "generatePass": "Generate Password", "myProfile": "My Profile", "changePassword": "Change Password", "oldPass": "Old Password", "newPass": "New Password", "newPassAgain": "New Password Again", "contracted": "Contracted", "shortTerm": "Short Term", "fullTime": "Full Time", "partTime": "Part Time", "location": "Location", "province": "Province", "district": "District", "neighborhood": "Neighborhood", "postalCode": "Postal Code", "country": "Country", "avenueStreet": "Avenue/Street", "buildingDoor": "Building/Door No", "addressDirections": "Address Directions", "regionalTime": "Regional Time", "address": "Address", "devices": "Devices", "deviceName": "Device Name", "newDevice": "New Device", "brand": "Brand", "model": "Model", "version": "Version", "showDevices": "Show Devices", "gsm": "GSM", "gps": "GPS", "calendar": "Calendar", "day": "Day", "week": "Week", "month": "Month", "today": "Today", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "showBusinessHours": "Show business hours", "showAllDay": "Show all day", "noEventsToday": "There are no events for this day!", "outOfHours": "Out of Hours", "calendarColor": "Calendar Color", "startTime": "Start Time", "endTime": "End Time", "time": "Time", "date": "Date", "dataTime": "Data Time", "lastDataTime": "Last Data Time", "timeInterval": "Time Interval", "duration": "Duration", "beginning": "Beginning", "finish": "End", "lastHour": "Last {number} hour", "jobList": "Job List", "newJob": "New Job", "newTask": "New Task", "task": "Task", "allJobs": "All Jobs", "importantJobs": "Important Jobs", "completedJobs": "Completed Jobs", "unfinishedJobs": "Unfinished Business", "reports": "Reports", "reportType": "Report Type", "movementReport": "Movement Report", "timeReport": "Time Report", "exportExcel": "Export to Excel", "exportPdf": "Export to PDF", "rules": "Rules", "ruleList": "Rule List", "addNewRule": "Add New Rule", "newRule": "New Rule", "speedViolation": "Excessive Speed Violation", "stopTimeViolation": "Instant Stop Time Violation", "movementTimeViolation": "Instantaneous Movement Time Violation", "dailyMaxStopTimeViolation": "Daily Maximum Stopping Time Violation", "dailyMovementTimeViolation": "Daily Movement Time Violation", "areaEntryExitViolation": "Area Entry/Exit Violation", "suddenAcceleration": "Sudden Acceleration", "suddenSlowdown": "Sudden Slowdown", "workingTimeDelayViolation": "Working Time Delay Violation", "fatigueViolation": "Fatigue Violation", "notifications": "Notifications", "totalViolation": "Total Violation", "instantStatus": "Instant Status", "instantStop": "Instant\nStop", "instantAct": "Instant\nAction", "wholeTeam": "Whole Team", "team": "Team", "teamGroup": "Team/Group", "person": "Person", "animation": "Animation", "animationColors": "Animation Colors", "performance": "Performance", "dailyMovementDistanceInKm": "Daily Movement Distance (km)", "dailyDrivingDistanceInKm": "Daily Driving Distance (km)", "maxSpeedInKm": "Maximum Speed(km)", "numberDailyAct": "Number of Daily Activities", "numberDevicesNoData": "Number of Devices with No Data", "distance": "Distance", "speed": "Speed", "kmh": "Km/h", "drivingTot": "Driving Tot.(km)", "movingTot": "Moving Tot.(km)", "headAddress": "Head Address", "lastAddress": "Last Address", "dayStartAddress": "Day Start Address", "dailyDowntime": "Daily Downtime", "dailyWalkingTime": "Daily Walking Time", "dailyWalkingDistance": "Daily Walking Distance (km)", "dailyDrivingTime": "Daily Driving Time", "dailyDrivingDistance": "Daily Driving Distance (km)", "vehicleTotalDistance": "Vehicle Total Distance (km)", "forms": "Forms", "settings": "Settings", "locationClosureAuthorization": "Location Closure Authorization", "closedPortfolio": "Closed Portfolio", "applicationInstalled": "Application Installed", "inAppLocationSharing": "In-app Location Sharing", "serviceIp": "Service IP", "servicePort": "Service Port", "companyUpdate": "Company Update", "minCharacterError": "The number of characters can be at least {charLength}", "enterValidId": "Please enter a valid customer number.", "enterValidEmail": "Please enter a valid email address.", "enterValidPass": "Please enter a valid password.", "enterValidOTP": "Please enter a valid OTP code.", "enterValidPostalCode": "Please enter a valid postal code.", "enterValidName": "Please enter a valid name.", "enterValidPhone": "Please enter a valid phone number.", "enterValidWebAddress": "Please enter a valid web address.", "enterValidIpAddress": "Please enter a valid server IP.", "enterValidServerPort": "Please enter a valid server port.", "enterValidLatLng": "Please enter a valid lat. long. format.", "enterValidBuildingDoor": "Please enter a valid building door no.", "otp": "OTP", "signingIn": "Signing In", "forgetPass": "Forget Password", "actType": "Activity Type", "eventType": "Event Type", "contentType": "Content Type", "conclusion": "Conclusion", "content": "Content", "contact": "Contact", "reason": "Reason", "searchNameTask": "Search (Name or Task)", "jan": "January", "feb": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "aug": "August", "sep": "September", "oct": "October", "nov": "November", "dec": "December", "permits": "Permits", "costs": "Costs", "enter": "Enter", "exit": "Exit", "customers": "Customers", "selectDevice": "Select Device", "critical": "Critical", "personnel": {"personnel": "Personnel", "personnels": "Personnels", "personalManagement": "Personal Management", "personalDetail": "Personal Detail", "addNewPersonnel": "Add New Personnel", "addNewPersonnelPlus": "Add New Personnel +", "editPersonnel": "Edit Personnel", "searchPersonnel": "Search Personnel", "personnelName": "Personnel Name", "noPersonnelFound": "No personnel found", "personalInfo": "Personal Information", "workInfo": "Work Information", "contactInfo": "Contact Information", "workDetails": "Work Details", "educationInfo": "Education Information", "documents": "Documents", "personalDocuments": "Personal Documents", "workType": "Work Type", "workStartDate": "Work Start Date", "jobStartDate": "Job Start Date", "assignmentDate": "Assignment Date", "department": "Department", "position": "Position", "grossSalary": "Gross Salary", "nationality": "Nationality", "turkish": "Turkish", "gender": "Gender", "male": "Male", "female": "Female", "birthDate": "Birth Date", "maritalStatus": "Marital Status", "married": "Married", "single": "Single", "numberOfChildren": "Number of Children", "identityNumber": "Identity Number", "personalPhone": "Personal Phone", "personalEmail": "Personal Email Address", "workPhone": "Work Phone", "workEmail": "Work Email Address", "employee": "Employee", "employees": "Employees"}, "education": {"educationRecords": "Education Records", "addEducationRecord": "Add Education Record", "addEducationRecordPlus": "Add Education Record +", "educationInstitution": "Education Institution", "graduationDate": "Graduation Date"}, "document": {"addDocumentRecord": "Add Document Record", "documentType": "Document Type", "missingDocument": "Missing Document", "viewDocument": "View Document", "updateDocument": "Update Document", "uploadDocument": "Upload Document", "addFile": "Add File", "uploadingFiles": "Uploading files, please wait...", "youCanOnlyUploadUpTo": "You can only upload up to {maxFileCount} files"}, "leave": {"leaveCalendar": "Leave Calendar", "leaveTypes": "Leave Types", "leaveType": "Leave Type", "leaveUsage": "Leave Usage", "leaveStart": "Leave Start", "leaveEnd": "Leave End", "leaveDuration": "Leave Duration", "addLeaveRecord": "Add Leave Record", "addLeaveRecordPlus": "Add Leave Record +", "annualPaidLeave": "Annual paid leave", "maternityLeave": "Maternity leave", "paternityLeave": "Paternity leave", "marriageLeave": "Marriage leave", "adoptionLeave": "Adoption leave", "breastfeedingLeave": "Breastfeeding leave", "halfDayMaternityLeave": "Half day maternity leave", "excuseLeave": "Excuse leave", "periodicControlLeave": "Periodic control leave", "disabledChildTreatmentLeave": "Disabled child treatment leave", "newJobSearchLeave": "New job search leave", "remainingAnnualLeaveRight": "Remaining Annual Leave Right", "usedAnnualLeave": "Used Annual Leave"}, "assignment": {"assignmentAndSalaryInfo": "Assignment and Salary Information", "addAssignmentSalaryRecord": "Add Assignment and Salary Information Record", "addAssignmentSalaryRecordPlus": "Add Assignment and Salary Information Record +"}, "asset": {"assignedAssets": "Assigned Assets", "addAsset": "Add <PERSON>set", "addAssetRecord": "Add Asset Record", "assetType": "Asset Type", "returnDate": "Return Date"}}